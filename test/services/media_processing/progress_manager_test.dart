import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/media_processing/progress_manager.dart';
import 'package:fishing_app/services/media_processing/progress_models.dart';
import 'package:fishing_app/services/media_processing/progress_listener.dart';

void main() {
  group('ProgressManager Tests', () {
    late ProgressManager progressManager;

    setUp(() {
      progressManager = ProgressManager.instance;
      // 清理之前的任务
      progressManager.clearAllTasks();
    });

    tearDown(() {
      progressManager.clearAllTasks();
    });

    group('Task Management', () {
      test('should create task successfully', () {
        final taskId = 'test_task_1';
        final task = progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
          fileName: 'test.jpg',
          fileSize: 1024,
        );

        expect(task.taskId, equals(taskId));
        expect(task.taskType, equals(TaskType.imageProcessing));
        expect(task.status, equals(TaskProgressStatus.pending));
        expect(task.progress, equals(0.0));
        expect(task.fileName, equals('test.jpg'));
        expect(task.fileSize, equals(1024));
      });

      test('should update task progress', () {
        final taskId = 'test_task_2';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        final updatedTask = progressManager.updateTaskProgress(
          taskId: taskId,
          progress: 0.5,
          message: '处理中',
          status: TaskProgressStatus.processing,
        );

        expect(updatedTask, isNotNull);
        expect(updatedTask!.progress, equals(0.5));
        expect(updatedTask.message, equals('处理中'));
        expect(updatedTask.status, equals(TaskProgressStatus.processing));
      });

      test('should complete task successfully', () {
        final taskId = 'test_task_3';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        final completedTask = progressManager.completeTask(
          taskId,
          message: '处理完成',
        );

        expect(completedTask, isNotNull);
        expect(completedTask!.status, equals(TaskProgressStatus.completed));
        expect(completedTask.progress, equals(1.0));
        expect(completedTask.message, equals('处理完成'));
        expect(completedTask.isSuccessful, isTrue);
        expect(completedTask.isFinished, isTrue);
      });

      test('should fail task with error', () {
        final taskId = 'test_task_4';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        final failedTask = progressManager.failTask(
          taskId,
          error: '处理失败',
          message: '发生错误',
        );

        expect(failedTask, isNotNull);
        expect(failedTask!.status, equals(TaskProgressStatus.failed));
        expect(failedTask.error, equals('处理失败'));
        expect(failedTask.message, equals('发生错误'));
        expect(failedTask.isFailed, isTrue);
        expect(failedTask.isFinished, isTrue);
      });

      test('should cancel task', () {
        final taskId = 'test_task_5';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        final cancelledTask = progressManager.cancelTask(
          taskId,
          message: '用户取消',
        );

        expect(cancelledTask, isNotNull);
        expect(cancelledTask!.status, equals(TaskProgressStatus.cancelled));
        expect(cancelledTask.message, equals('用户取消'));
        expect(cancelledTask.isCancelled, isTrue);
        expect(cancelledTask.isFinished, isTrue);
      });

      test('should get task progress', () {
        final taskId = 'test_task_6';
        final originalTask = progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.videoProcessing,
        );

        final retrievedTask = progressManager.getTaskProgress(taskId);
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.taskId, equals(originalTask.taskId));
        expect(retrievedTask.taskType, equals(originalTask.taskType));
      });

      test('should return null for non-existent task', () {
        final task = progressManager.getTaskProgress('non_existent_task');
        expect(task, isNull);
      });
    });

    group('Batch Task Management', () {
      test('should create batch task', () {
        final batchId = 'batch_task_1';
        final subTaskIds = ['sub_1', 'sub_2', 'sub_3'];

        final batchTask = progressManager.createBatchTask(
          batchId: batchId,
          taskIds: subTaskIds,
          metadata: {'description': 'Test batch'},
        );

        expect(batchTask.taskId, equals(batchId));
        expect(batchTask.taskType, equals(TaskType.batchProcessing));
        expect(batchTask.metadata['task_count'], equals(3));
        expect(batchTask.metadata['sub_tasks'], equals(subTaskIds));
      });

      test('should calculate batch progress correctly', () {
        final batchId = 'batch_task_2';
        final subTaskIds = ['sub_1', 'sub_2'];

        // 创建批量任务
        progressManager.createBatchTask(
          batchId: batchId,
          taskIds: subTaskIds,
        );

        // 创建子任务
        for (final subTaskId in subTaskIds) {
          progressManager.createTask(
            taskId: subTaskId,
            taskType: TaskType.imageProcessing,
          );
        }

        // 更新子任务进度
        progressManager.updateTaskProgress(
          taskId: 'sub_1',
          progress: 0.5,
          status: TaskProgressStatus.processing,
        );
        progressManager.completeTask('sub_2');

        // 获取批量任务进度
        final batchProgress = progressManager.getBatchProgress(batchId);
        expect(batchProgress, isNotNull);
        expect(batchProgress!.progress, equals(0.75)); // (0.5 + 1.0) / 2
      });
    });

    group('Task Filtering', () {
      test('should get running tasks', () {
        // 创建不同状态的任务
        progressManager.createTask(taskId: 'task_1', taskType: TaskType.imageProcessing);
        progressManager.createTask(taskId: 'task_2', taskType: TaskType.videoProcessing);
        progressManager.createTask(taskId: 'task_3', taskType: TaskType.imageProcessing);

        // 更新任务状态
        progressManager.updateTaskProgress(
          taskId: 'task_1',
          status: TaskProgressStatus.processing,
        );
        progressManager.updateTaskProgress(
          taskId: 'task_2',
          status: TaskProgressStatus.uploading,
        );
        progressManager.completeTask('task_3');

        final runningTasks = progressManager.getRunningTasks();
        expect(runningTasks.length, equals(2));
        expect(runningTasks.any((task) => task.taskId == 'task_1'), isTrue);
        expect(runningTasks.any((task) => task.taskId == 'task_2'), isTrue);
      });

      test('should get completed tasks', () {
        // 创建任务
        progressManager.createTask(taskId: 'task_1', taskType: TaskType.imageProcessing);
        progressManager.createTask(taskId: 'task_2', taskType: TaskType.videoProcessing);

        // 完成一个任务
        progressManager.completeTask('task_1');
        progressManager.failTask('task_2', error: 'Test error');

        final completedTasks = progressManager.getCompletedTasks();
        expect(completedTasks.length, equals(1));
        expect(completedTasks.first.taskId, equals('task_1'));
      });

      test('should get failed tasks', () {
        // 创建任务
        progressManager.createTask(taskId: 'task_1', taskType: TaskType.imageProcessing);
        progressManager.createTask(taskId: 'task_2', taskType: TaskType.videoProcessing);

        // 失败一个任务
        progressManager.completeTask('task_1');
        progressManager.failTask('task_2', error: 'Test error');

        final failedTasks = progressManager.getFailedTasks();
        expect(failedTasks.length, equals(1));
        expect(failedTasks.first.taskId, equals('task_2'));
      });
    });

    group('Progress Listeners', () {
      test('should notify global listeners', () async {
        final completer = Completer<ProgressEvent>();
        
        final listener = FunctionProgressListener(
          listenerId: 'test_listener',
          onProgressEvent: (event) {
            if (!completer.isCompleted) {
              completer.complete(event);
            }
          },
        );

        progressManager.addGlobalListener(listener);

        // 创建任务触发事件
        final taskId = 'test_task';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        // 等待事件
        final event = await completer.future.timeout(Duration(seconds: 1));
        expect(event.taskProgress.taskId, equals(taskId));
        expect(event.type, equals(ProgressEventType.statusChanged));

        progressManager.removeGlobalListener('test_listener');
      });

      test('should notify task-specific listeners', () async {
        final completer = Completer<ProgressEvent>();
        final taskId = 'test_task';
        
        final listener = FunctionProgressListener(
          listenerId: 'task_listener',
          onProgressEvent: (event) {
            if (event.type == ProgressEventType.progressUpdated && !completer.isCompleted) {
              completer.complete(event);
            }
          },
        );

        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );
        progressManager.addTaskListener(taskId, listener);

        // 更新进度触发事件
        progressManager.updateTaskProgress(
          taskId: taskId,
          progress: 0.5,
        );

        // 等待事件
        final event = await completer.future.timeout(Duration(seconds: 1));
        expect(event.taskProgress.taskId, equals(taskId));
        expect(event.taskProgress.progress, equals(0.5));

        progressManager.removeTaskListener(taskId, 'task_listener');
      });
    });

    group('Statistics', () {
      test('should provide accurate statistics', () {
        // 创建不同状态的任务
        progressManager.createTask(taskId: 'task_1', taskType: TaskType.imageProcessing);
        progressManager.createTask(taskId: 'task_2', taskType: TaskType.videoProcessing);
        progressManager.createTask(taskId: 'task_3', taskType: TaskType.imageProcessing);

        progressManager.updateTaskProgress(taskId: 'task_1', status: TaskProgressStatus.processing);
        progressManager.completeTask('task_2');
        progressManager.failTask('task_3', error: 'Test error');

        final stats = progressManager.getStatistics();
        expect(stats['total_tasks'], equals(3));
        expect(stats['running_tasks'], equals(1));
        expect(stats['completed_tasks'], equals(1));
        expect(stats['failed_tasks'], equals(1));
      });
    });

    group('Utility Functions', () {
      test('should generate unique task IDs', () {
        final id1 = ProgressManager.generateTaskId();
        final id2 = ProgressManager.generateTaskId();
        
        expect(id1, isNotEmpty);
        expect(id2, isNotEmpty);
        expect(id1, isNot(equals(id2)));
        expect(id1, startsWith('task_'));
        expect(id2, startsWith('task_'));
      });

      test('should check cancellation status', () {
        final taskId = 'test_task';
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        expect(progressManager.shouldCancel(taskId), isFalse);

        progressManager.cancelTask(taskId);
        expect(progressManager.shouldCancel(taskId), isTrue);
      });
    });
  });
}
