import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/media_processing/video_compress_queue.dart';
import 'package:video_compress/video_compress.dart';

void main() {
  group('VideoCompressQueue Tests', () {
    late VideoCompressQueue queue;

    setUp(() {
      queue = VideoCompressQueue.instance;
      queue.clearQueue();
    });

    tearDown(() {
      queue.clearQueue();
    });

    group('Queue Management', () {
      test('should initialize with empty queue', () {
        expect(queue.queueLength, equals(0));
        expect(queue.isProcessing, isFalse);
        expect(queue.currentTaskId, isNull);
        expect(queue.pendingTaskIds, isEmpty);
      });

      test('should add task to queue', () async {
        final testFile = File('test_video.mp4');
        
        // 模拟添加任务（不实际执行压缩）
        final taskFuture = queue.addTask(
          taskId: 'test_task_1',
          inputFile: testFile,
          quality: VideoQuality.DefaultQuality,
        );

        // 验证队列状态
        expect(queue.queueLength >= 0, isTrue); // 可能立即开始处理
        expect(queue.pendingTaskIds.contains('test_task_1') || queue.currentTaskId == 'test_task_1', isTrue);

        // 取消任务避免实际执行
        queue.cancelTask('test_task_1');
        
        try {
          await taskFuture;
        } catch (e) {
          // 预期可能会有取消异常
        }
      });

      test('should cancel task in queue', () async {
        final testFile = File('test_video.mp4');
        
        // 添加任务
        final taskFuture = queue.addTask(
          taskId: 'cancel_test',
          inputFile: testFile,
        );

        // 立即取消
        final cancelled = queue.cancelTask('cancel_test');
        expect(cancelled, isTrue);

        // 验证任务被取消
        final result = await taskFuture;
        expect(result, isNull);
      });

      test('should get queue status', () {
        final status = queue.getQueueStatus();
        
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('queue_length'), isTrue);
        expect(status.containsKey('is_processing'), isTrue);
        expect(status.containsKey('current_task_id'), isTrue);
        expect(status.containsKey('pending_tasks'), isTrue);
      });

      test('should estimate wait time', () {
        final waitTime = queue.estimateWaitTime();
        expect(waitTime, isA<double>());
        expect(waitTime, greaterThanOrEqualTo(0.0));
      });

      test('should get task position', () {
        final testFile = File('test_video.mp4');
        
        // 添加任务
        queue.addTask(
          taskId: 'position_test',
          inputFile: testFile,
        );

        final position = queue.getTaskPosition('position_test');
        expect(position, greaterThanOrEqualTo(0)); // 0表示正在处理，>0表示在队列中

        // 清理
        queue.cancelTask('position_test');
      });

      test('should clear all tasks', () async {
        final testFile = File('test_video.mp4');
        
        // 添加多个任务
        final task1 = queue.addTask(taskId: 'clear_test_1', inputFile: testFile);
        final task2 = queue.addTask(taskId: 'clear_test_2', inputFile: testFile);

        // 清空队列
        queue.clearQueue();

        // 验证队列为空
        expect(queue.queueLength, equals(0));
        expect(queue.isProcessing, isFalse);
        expect(queue.currentTaskId, isNull);

        // 验证任务被取消
        final result1 = await task1;
        final result2 = await task2;
        expect(result1, isNull);
        expect(result2, isNull);
      });
    });

    group('Concurrent Processing', () {
      test('should handle multiple tasks sequentially', () async {
        final testFile = File('test_video.mp4');
        
        // 添加多个任务
        final tasks = <Future<dynamic>>[];
        for (int i = 0; i < 3; i++) {
          tasks.add(queue.addTask(
            taskId: 'concurrent_test_$i',
            inputFile: testFile,
          ));
        }

        // 验证队列管理
        final status = queue.getQueueStatus();
        expect(status['queue_length'] + (status['is_processing'] ? 1 : 0), equals(3));

        // 取消所有任务
        for (int i = 0; i < 3; i++) {
          queue.cancelTask('concurrent_test_$i');
        }

        // 等待所有任务完成
        final results = await Future.wait(tasks);
        for (final result in results) {
          expect(result, isNull); // 所有任务都应该被取消
        }
      });

      test('should prevent concurrent compression', () async {
        final testFile = File('test_video.mp4');
        
        // 同时添加两个任务
        final task1 = queue.addTask(taskId: 'prevent_test_1', inputFile: testFile);
        final task2 = queue.addTask(taskId: 'prevent_test_2', inputFile: testFile);

        // 验证只有一个任务在处理，另一个在队列中
        await Future.delayed(Duration(milliseconds: 100));
        final status = queue.getQueueStatus();
        
        if (status['is_processing']) {
          expect(status['queue_length'], greaterThanOrEqualTo(0));
        }

        // 清理
        queue.cancelTask('prevent_test_1');
        queue.cancelTask('prevent_test_2');

        try {
          await Future.wait([task1, task2]);
        } catch (e) {
          // 预期可能有异常
        }
      });
    });

    group('Error Handling', () {
      test('should handle invalid file gracefully', () async {
        final invalidFile = File('non_existent_file.mp4');
        
        final taskFuture = queue.addTask(
          taskId: 'invalid_file_test',
          inputFile: invalidFile,
        );

        try {
          final result = await taskFuture;
          // 如果没有抛出异常，结果应该为null
          expect(result, isNull);
        } catch (e) {
          // 预期可能会抛出异常
          expect(e, isNotNull);
        }
      });

      test('should handle task cancellation during processing', () async {
        final testFile = File('test_video.mp4');
        
        final taskFuture = queue.addTask(
          taskId: 'cancel_during_process',
          inputFile: testFile,
        );

        // 短暂延迟后取消
        await Future.delayed(Duration(milliseconds: 50));
        final cancelled = queue.cancelTask('cancel_during_process');
        
        final result = await taskFuture;
        expect(result, isNull);
      });
    });

    group('Progress Tracking', () {
      test('should track progress with callback', () async {
        final testFile = File('test_video.mp4');
        final progressUpdates = <double>[];
        
        final taskFuture = queue.addTask(
          taskId: 'progress_test',
          inputFile: testFile,
          onProgress: (progress) {
            progressUpdates.add(progress);
          },
        );

        // 立即取消以避免实际压缩
        queue.cancelTask('progress_test');
        
        try {
          await taskFuture;
        } catch (e) {
          // 预期可能有异常
        }

        // 验证进度回调机制存在（即使没有实际进度更新）
        expect(progressUpdates, isA<List<double>>());
      });
    });

    group('Resource Management', () {
      test('should dispose resources properly', () {
        // 添加一些任务
        final testFile = File('test_video.mp4');
        queue.addTask(taskId: 'dispose_test', inputFile: testFile);

        // 释放资源
        queue.dispose();

        // 验证队列被清空
        expect(queue.queueLength, equals(0));
        expect(queue.isProcessing, isFalse);
      });
    });
  });
}
