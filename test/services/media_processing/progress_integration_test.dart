import 'dart:async';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/media_processing/progress_manager.dart';
import 'package:fishing_app/services/media_processing/progress_models.dart';
import 'package:fishing_app/services/media_processing/progress_listener.dart';
import 'package:fishing_app/services/media_processing/unified_media_service.dart';

void main() {
  group('Progress Integration Tests', () {
    late ProgressManager progressManager;
    late UnifiedMediaService mediaService;

    setUp(() {
      progressManager = ProgressManager.instance;
      mediaService = UnifiedMediaService.instance;
      progressManager.clearAllTasks();
    });

    tearDown(() {
      progressManager.clearAllTasks();
    });

    group('Multi-Task Concurrent Processing', () {
      test('should handle multiple concurrent tasks', () async {
        final taskIds = ['task_1', 'task_2', 'task_3'];
        final completedTasks = <String>[];
        final progressUpdates = <String, List<double>>{};

        // 创建监听器收集事件
        final listener = FunctionProgressListener(
          listenerId: 'concurrent_listener',
          onProgressEvent: (event) {
            final taskId = event.taskProgress.taskId;
            progressUpdates.putIfAbsent(taskId, () => []);
            progressUpdates[taskId]!.add(event.taskProgress.progress);
          },
          onTaskCompleted: (taskId, progress) {
            completedTasks.add(taskId);
          },
        );

        progressManager.addGlobalListener(listener);

        // 并发创建和处理任务
        final futures = taskIds.map((taskId) async {
          // 创建任务
          progressManager.createTask(
            taskId: taskId,
            taskType: TaskType.imageProcessing,
            fileName: '$taskId.jpg',
          );

          // 模拟处理过程
          for (int i = 1; i <= 10; i++) {
            await Future.delayed(Duration(milliseconds: 10));
            progressManager.updateTaskProgress(
              taskId: taskId,
              progress: i / 10.0,
              message: '处理步骤 $i/10',
              status: i < 10 ? TaskProgressStatus.processing : TaskProgressStatus.completed,
            );
          }

          // 完成任务
          progressManager.completeTask(taskId);
        }).toList();

        // 等待所有任务完成
        await Future.wait(futures);

        // 验证结果
        expect(completedTasks.length, equals(3));
        expect(completedTasks.toSet(), equals(taskIds.toSet()));

        // 验证每个任务都有进度更新
        for (final taskId in taskIds) {
          expect(progressUpdates[taskId], isNotNull);
          expect(progressUpdates[taskId]!.length, greaterThan(0));
          expect(progressUpdates[taskId]!.last, equals(1.0));
        }

        progressManager.removeGlobalListener('concurrent_listener');
      });

      test('should handle task failures in concurrent processing', () async {
        final taskIds = ['success_task', 'fail_task', 'cancel_task'];
        final taskResults = <String, TaskProgressStatus>{};

        final listener = FunctionProgressListener(
          listenerId: 'failure_listener',
          onProgressEvent: (event) {
            if (event.taskProgress.isFinished) {
              taskResults[event.taskProgress.taskId] = event.taskProgress.status;
            }
          },
        );

        progressManager.addGlobalListener(listener);

        // 并发处理不同结果的任务
        final futures = [
          // 成功任务
          () async {
            progressManager.createTask(
              taskId: 'success_task',
              taskType: TaskType.imageProcessing,
            );
            await Future.delayed(Duration(milliseconds: 50));
            progressManager.completeTask('success_task');
          }(),

          // 失败任务
          () async {
            progressManager.createTask(
              taskId: 'fail_task',
              taskType: TaskType.videoProcessing,
            );
            await Future.delayed(Duration(milliseconds: 30));
            progressManager.failTask('fail_task', error: '模拟错误');
          }(),

          // 取消任务
          () async {
            progressManager.createTask(
              taskId: 'cancel_task',
              taskType: TaskType.imageProcessing,
            );
            await Future.delayed(Duration(milliseconds: 20));
            progressManager.cancelTask('cancel_task');
          }(),
        ];

        await Future.wait(futures);

        // 验证不同的任务结果
        expect(taskResults['success_task'], equals(TaskProgressStatus.completed));
        expect(taskResults['fail_task'], equals(TaskProgressStatus.failed));
        expect(taskResults['cancel_task'], equals(TaskProgressStatus.cancelled));

        progressManager.removeGlobalListener('failure_listener');
      });
    });

    group('Batch Task Processing', () {
      test('should aggregate batch progress correctly', () async {
        final batchId = 'integration_batch';
        final subTaskIds = ['sub_1', 'sub_2', 'sub_3', 'sub_4'];

        // 创建批量任务
        progressManager.createBatchTask(
          batchId: batchId,
          taskIds: subTaskIds,
        );

        // 创建子任务
        for (final subTaskId in subTaskIds) {
          progressManager.createTask(
            taskId: subTaskId,
            taskType: TaskType.imageProcessing,
          );
        }

        // 模拟不同的处理进度
        progressManager.updateTaskProgress(taskId: 'sub_1', progress: 1.0, status: TaskProgressStatus.completed);
        progressManager.updateTaskProgress(taskId: 'sub_2', progress: 0.5, status: TaskProgressStatus.processing);
        progressManager.updateTaskProgress(taskId: 'sub_3', progress: 0.8, status: TaskProgressStatus.processing);
        progressManager.updateTaskProgress(taskId: 'sub_4', progress: 0.0, status: TaskProgressStatus.pending);

        // 获取批量进度
        final batchProgress = progressManager.getBatchProgress(batchId);
        expect(batchProgress, isNotNull);

        // 验证聚合进度 (1.0 + 0.5 + 0.8 + 0.0) / 4 = 0.575
        expect(batchProgress!.progress, closeTo(0.575, 0.01));
        expect(batchProgress.status, equals(TaskProgressStatus.processing));

        // 完成所有子任务
        progressManager.completeTask('sub_2');
        progressManager.completeTask('sub_3');
        progressManager.completeTask('sub_4');

        // 再次检查批量进度
        final finalBatchProgress = progressManager.getBatchProgress(batchId);
        expect(finalBatchProgress!.progress, equals(1.0));
        expect(finalBatchProgress.status, equals(TaskProgressStatus.completed));
      });

      test('should handle batch task with failures', () async {
        final batchId = 'mixed_batch';
        final subTaskIds = ['batch_sub_1', 'batch_sub_2', 'batch_sub_3'];

        progressManager.createBatchTask(
          batchId: batchId,
          taskIds: subTaskIds,
        );

        // 创建子任务
        for (final subTaskId in subTaskIds) {
          progressManager.createTask(
            taskId: subTaskId,
            taskType: TaskType.imageProcessing,
          );
        }

        // 模拟混合结果
        progressManager.completeTask('batch_sub_1');
        progressManager.failTask('batch_sub_2', error: '处理失败');
        progressManager.cancelTask('batch_sub_3');

        final batchProgress = progressManager.getBatchProgress(batchId);
        expect(batchProgress, isNotNull);
        expect(batchProgress!.status, equals(TaskProgressStatus.failed));
        expect(batchProgress.metadata['completed_count'], equals(1));
        expect(batchProgress.metadata['failed_count'], equals(1));
        expect(batchProgress.metadata['cancelled_count'], equals(1));
      });
    });

    group('Listener System Integration', () {
      test('should handle multiple listeners correctly', () async {
        final taskId = 'multi_listener_task';
        final listener1Events = <ProgressEvent>[];
        final listener2Events = <ProgressEvent>[];
        final taskSpecificEvents = <ProgressEvent>[];

        // 全局监听器1
        final globalListener1 = FunctionProgressListener(
          listenerId: 'global_1',
          onProgressEvent: (event) => listener1Events.add(event),
        );

        // 全局监听器2
        final globalListener2 = FunctionProgressListener(
          listenerId: 'global_2',
          onProgressEvent: (event) => listener2Events.add(event),
        );

        // 任务特定监听器
        final taskListener = FunctionProgressListener(
          listenerId: 'task_specific',
          onProgressEvent: (event) => taskSpecificEvents.add(event),
        );

        progressManager.addGlobalListener(globalListener1);
        progressManager.addGlobalListener(globalListener2);

        // 创建任务
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        progressManager.addTaskListener(taskId, taskListener);

        // 触发多个事件
        progressManager.updateTaskProgress(taskId: taskId, progress: 0.3);
        progressManager.updateTaskProgress(taskId: taskId, progress: 0.7);
        progressManager.completeTask(taskId);

        // 等待事件传播
        await Future.delayed(Duration(milliseconds: 10));

        // 验证所有监听器都收到了事件
        expect(listener1Events.length, greaterThan(0));
        expect(listener2Events.length, greaterThan(0));
        expect(taskSpecificEvents.length, greaterThan(0));

        // 验证事件内容一致
        expect(listener1Events.length, equals(listener2Events.length));
        expect(listener1Events.length, equals(taskSpecificEvents.length));

        // 清理监听器
        progressManager.removeGlobalListener('global_1');
        progressManager.removeGlobalListener('global_2');
        progressManager.removeTaskListener(taskId, 'task_specific');
      });

      test('should handle listener errors gracefully', () async {
        final taskId = 'error_listener_task';
        var errorListenerCalled = false;
        var normalListenerCalled = false;

        // 会抛出异常的监听器
        final errorListener = FunctionProgressListener(
          listenerId: 'error_listener',
          onProgressEvent: (event) {
            errorListenerCalled = true;
            throw Exception('监听器错误');
          },
        );

        // 正常的监听器
        final normalListener = FunctionProgressListener(
          listenerId: 'normal_listener',
          onProgressEvent: (event) {
            normalListenerCalled = true;
          },
        );

        progressManager.addGlobalListener(errorListener);
        progressManager.addGlobalListener(normalListener);

        // 创建任务触发事件
        progressManager.createTask(
          taskId: taskId,
          taskType: TaskType.imageProcessing,
        );

        await Future.delayed(Duration(milliseconds: 10));

        // 验证即使有监听器出错，其他监听器仍然正常工作
        expect(errorListenerCalled, isTrue);
        expect(normalListenerCalled, isTrue);

        progressManager.removeGlobalListener('error_listener');
        progressManager.removeGlobalListener('normal_listener');
      });
    });

    group('Memory and Performance', () {
      test('should handle large number of tasks efficiently', () async {
        final taskCount = 100;
        final taskIds = List.generate(taskCount, (i) => 'perf_task_$i');

        final startTime = DateTime.now();

        // 创建大量任务
        for (final taskId in taskIds) {
          progressManager.createTask(
            taskId: taskId,
            taskType: TaskType.imageProcessing,
          );
        }

        // 批量更新进度
        for (final taskId in taskIds) {
          progressManager.updateTaskProgress(
            taskId: taskId,
            progress: 0.5,
            status: TaskProgressStatus.processing,
          );
        }

        // 批量完成任务
        for (final taskId in taskIds) {
          progressManager.completeTask(taskId);
        }

        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);

        // 验证性能（应该在合理时间内完成）
        expect(duration.inMilliseconds, lessThan(1000)); // 1秒内完成

        // 验证统计信息
        final stats = progressManager.getStatistics();
        expect(stats['total_tasks'], equals(taskCount));
        expect(stats['completed_tasks'], equals(taskCount));
      });

      test('should cleanup finished tasks properly', () async {
        final taskIds = ['cleanup_1', 'cleanup_2', 'cleanup_3'];

        // 创建并完成任务
        for (final taskId in taskIds) {
          progressManager.createTask(
            taskId: taskId,
            taskType: TaskType.imageProcessing,
          );
          progressManager.completeTask(taskId);
        }

        // 验证任务存在
        expect(progressManager.getAllTasks().length, equals(3));

        // 清理已完成的任务
        progressManager.cleanupFinishedTasks();

        // 验证任务被清理
        expect(progressManager.getAllTasks().length, equals(0));
      });
    });
  });
}
