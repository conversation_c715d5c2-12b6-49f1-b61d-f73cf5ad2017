import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/media_processing/unified_media_service.dart';
import 'package:fishing_app/services/media_processing/progress_manager.dart';
import 'package:fishing_app/services/media_processing/progress_models.dart';
import 'package:fishing_app/services/media_processing/progress_listener.dart';
import 'package:fishing_app/services/media_processing/compatibility_layer.dart';
import 'package:fishing_app/widgets/add_spot_form/image_upload_manager.dart';
import 'package:fishing_app/widgets/add_spot_form/image_upload_widget.dart';

void main() {
  group('Integration Compatibility Tests', () {
    late UnifiedMediaService mediaService;
    late ProgressManager progressManager;
    late CompatibilityLayer compatibilityLayer;
    late ImageUploadManager uploadManager;

    setUp(() {
      mediaService = UnifiedMediaService.instance;
      progressManager = ProgressManager.instance;
      compatibilityLayer = CompatibilityLayer.instance;
      uploadManager = ImageUploadManager();

      // 清理之前的状态
      progressManager.clearAllTasks();
      mediaService.clearCache();
    });

    tearDown(() {
      progressManager.clearAllTasks();
      mediaService.clearCache();
    });

    group('UnifiedMediaService Integration', () {
      test('should integrate with progress manager correctly', () async {
        var progressUpdates = <double>[];
        var statusUpdates = <TaskProgressStatus>[];

        // 添加进度监听器
        final listener = FunctionProgressListener(
          listenerId: 'integration_test',
          onProgressEvent: (event) {
            progressUpdates.add(event.taskProgress.progress);
            statusUpdates.add(event.taskProgress.status);
          },
        );

        mediaService.addProgressListener(listener);

        // 创建测试文件
        final testFile = await _createTestImageFile();

        try {
          // 模拟媒体处理（这里会因为没有真实的上传服务而失败，但我们主要测试进度管理）
          await mediaService.processMedia(
            file: testFile,
            userId: 'test_user',
            progressCallback: (progress, message) {
              // 验证进度回调正常工作
              expect(progress, greaterThanOrEqualTo(0.0));
              expect(progress, lessThanOrEqualTo(1.0));
            },
          );
        } catch (e) {
          // 预期会失败，因为没有真实的上传服务
          print('预期的处理失败: $e');
        }

        // 验证进度管理器收到了事件
        expect(progressUpdates.isNotEmpty, isTrue);
        expect(statusUpdates.isNotEmpty, isTrue);

        // 验证状态变化序列
        expect(statusUpdates.first, equals(TaskProgressStatus.pending));

        mediaService.removeProgressListener('integration_test');
        await testFile.delete();
      });

      test('should handle task cancellation correctly', () async {
        final testFile = await _createTestImageFile();
        var taskId = '';

        // 添加监听器获取任务ID
        final listener = FunctionProgressListener(
          listenerId: 'cancel_test',
          onProgressEvent: (event) {
            if (taskId.isEmpty) {
              taskId = event.taskProgress.taskId;
            }
          },
        );

        mediaService.addProgressListener(listener);

        // 开始处理（异步）
        final processingFuture = mediaService.processMedia(
          file: testFile,
          userId: 'test_user',
        );

        // 等待任务开始
        await Future.delayed(Duration(milliseconds: 100));

        // 取消任务
        if (taskId.isNotEmpty) {
          final cancelled = mediaService.cancelTask(taskId);
          expect(cancelled, isTrue);

          // 验证任务状态
          final taskProgress = mediaService.getTaskProgress(taskId);
          expect(taskProgress?.isCancelled, isTrue);
        }

        try {
          await processingFuture;
        } catch (e) {
          // 预期可能会有异常
        }

        mediaService.removeProgressListener('cancel_test');
        await testFile.delete();
      });
    });

    group('Compatibility Layer Tests', () {
      test('should provide backward compatibility for image upload', () async {
        final testFile = await _createTestImageFile();

        try {
          // 使用兼容层的旧接口
          final result = await compatibilityLayer.uploadImageLegacy(
            imageFile: testFile,
            userId: 'test_user',
          );

          // 即使失败，也应该返回null而不是抛出异常
          // 这验证了兼容层的错误处理
          expect(result, isNull);
        } catch (e) {
          // 如果抛出异常，应该是可预期的网络或服务错误
          expect(e.toString(), contains('网络'));
        }

        await testFile.delete();
      });

      test('should provide backward compatibility for video upload', () async {
        final testFile = await _createTestVideoFile();

        try {
          final result = await compatibilityLayer.uploadVideoLegacy(
            videoFile: testFile,
            userId: 'test_user',
          );

          expect(result, isNull);
        } catch (e) {
          expect(e.toString(), contains('网络'));
        }

        await testFile.delete();
      });

      test('should get compatibility statistics', () {
        final stats = compatibilityLayer.getCompatibilityStats();

        expect(stats['new_service_available'], isTrue);
        expect(stats['legacy_service_available'], isTrue);
        expect(stats['fallback_enabled'], isTrue);
        expect(stats['supported_formats'], isA<List>());
      });
    });

    group('ImageUploadManager Integration', () {
      test('should work with new media service', () async {
        final testFile = await _createTestImageFile();
        final uploadItem = ImageUploadItem(
          file: testFile,
          mediaType: MediaType.image,
        );

        var progressUpdated = false;
        var itemUpdated = false;

        // 模拟上传过程
        try {
          await uploadManager.uploadSelectedImages([uploadItem], (item) {
            itemUpdated = true;
            if (item.uploadProgress > 0) {
              progressUpdated = true;
            }
          });
        } catch (e) {
          // 预期会失败
        }

        // 验证回调被调用
        expect(itemUpdated, isTrue);

        await testFile.delete();
      });
    });

    group('Error Handling Integration', () {
      test('should handle errors gracefully across components', () async {
        final invalidFile = File('non_existent_file.jpg');

        // 测试统一媒体服务的错误处理
        try {
          await mediaService.processMedia(
            file: invalidFile,
            userId: 'test_user',
          );
          fail('应该抛出异常');
        } catch (e) {
          expect(e.toString(), contains('文件不存在'));
        }

        // 测试兼容层的错误处理
        final result = await compatibilityLayer.uploadImageLegacy(
          imageFile: invalidFile,
          userId: 'test_user',
        );
        expect(result, isNull);
      });

      test('should maintain progress state during errors', () async {
        final testFile = await _createTestImageFile();
        var errorTaskId = '';

        final listener = FunctionProgressListener(
          listenerId: 'error_test',
          onProgressEvent: (event) {
            errorTaskId = event.taskProgress.taskId;
          },
          onError: (taskId, error) {
            expect(taskId, equals(errorTaskId));
            expect(error, isNotEmpty);
          },
        );

        mediaService.addProgressListener(listener);

        try {
          await mediaService.processMedia(file: testFile, userId: 'test_user');
        } catch (e) {
          // 预期的错误
        }

        // 验证任务状态被正确更新
        if (errorTaskId.isNotEmpty) {
          final taskProgress = mediaService.getTaskProgress(errorTaskId);
          expect(taskProgress?.isFailed, isTrue);
        }

        mediaService.removeProgressListener('error_test');
        await testFile.delete();
      });
    });

    group('Performance Integration', () {
      test('should handle multiple components efficiently', () async {
        final startTime = DateTime.now();
        final testFiles = <File>[];

        // 创建多个测试文件
        for (int i = 0; i < 5; i++) {
          testFiles.add(await _createTestImageFile());
        }

        try {
          // 并发处理多个文件
          final futures =
              testFiles
                  .map(
                    (file) => mediaService
                        .processMedia(file: file, userId: 'test_user')
                        .catchError((e) => null),
                  )
                  .toList();

          await Future.wait(futures);
        } catch (e) {
          // 预期可能有错误
        }

        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);

        // 验证性能合理
        expect(duration.inSeconds, lessThan(10));

        // 清理测试文件
        for (final file in testFiles) {
          await file.delete();
        }
      });
    });
  });
}

/// 创建测试图片文件
Future<File> _createTestImageFile() async {
  final tempDir = Directory.systemTemp;
  final testFile = File(
    '${tempDir.path}/test_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
  );

  // 创建一个简单的测试图片数据
  final imageData = Uint8List.fromList([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    // ... 简化的JPEG头部数据
  ]);

  await testFile.writeAsBytes(imageData);
  return testFile;
}

/// 创建测试视频文件
Future<File> _createTestVideoFile() async {
  final tempDir = Directory.systemTemp;
  final testFile = File(
    '${tempDir.path}/test_video_${DateTime.now().millisecondsSinceEpoch}.mp4',
  );

  // 创建一个简单的测试视频数据
  final videoData = Uint8List.fromList([
    0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x69, 0x73, 0x6F, 0x6D,
    0x00, 0x00, 0x02, 0x00, 0x69, 0x73, 0x6F, 0x6D, 0x69, 0x73, 0x6F, 0x32,
    // ... 简化的MP4头部数据
  ]);

  await testFile.writeAsBytes(videoData);
  return testFile;
}
