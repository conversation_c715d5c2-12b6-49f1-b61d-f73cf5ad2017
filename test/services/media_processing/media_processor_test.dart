import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:fishing_app/services/media_processing/media_processor.dart';
import 'package:fishing_app/services/media_processing/media_processor_factory.dart';
import 'package:fishing_app/services/media_processing/unified_media_service.dart';
import 'package:fishing_app/services/media_processing/smart_compression_strategy.dart';
import 'package:fishing_app/services/media_processing/memory_manager.dart';

void main() {
  group('MediaProcessor Tests', () {
    late MediaProcessorFactory factory;
    late UnifiedMediaService mediaService;

    setUpAll(() {
      factory = MediaProcessorFactory.instance;
      mediaService = UnifiedMediaService.instance;
      MemoryManager.instance.initialize();
    });

    tearDownAll(() {
      MemoryManager.instance.dispose();
    });

    group('MediaProcessorFactory', () {
      test('should return correct processor for image file', () {
        // 创建测试图片文件
        final imageFile = File('test_image.jpg');

        final processor = factory.getProcessorForFile(imageFile);
        expect(processor, isNotNull);
        expect(processor!.mediaType, equals(MediaType.image));
      });

      test('should return correct processor for video file', () {
        // 创建测试视频文件
        final videoFile = File('test_video.mp4');

        final processor = factory.getProcessorForFile(videoFile);
        expect(processor, isNotNull);
        expect(processor!.mediaType, equals(MediaType.video));
      });

      test('should return null for unsupported file', () {
        // 创建不支持的文件
        final unsupportedFile = File('test_file.txt');

        final processor = factory.getProcessorForFile(unsupportedFile);
        expect(processor, isNull);
      });

      test('should create default config for image', () {
        final config = factory.createDefaultConfig(MediaType.image);

        expect(config.quality, greaterThan(0));
        expect(config.quality, lessThanOrEqualTo(100));
        expect(config.generateThumbnail, isTrue);
      });

      test('should create default config for video', () {
        final config = factory.createDefaultConfig(MediaType.video);

        expect(config.quality, greaterThan(0));
        expect(config.quality, lessThanOrEqualTo(100));
        expect(config.generateThumbnail, isTrue);
      });
    });

    group('MediaProcessingConfig', () {
      test('should create image config with correct defaults', () {
        final config = MediaProcessingConfig.forImage();

        expect(config.quality, equals(85));
        expect(config.generateThumbnail, isTrue);
        expect(config.thumbnailQuality, equals(80));
      });

      test('should create video config with correct defaults', () {
        final config = MediaProcessingConfig.forVideo();

        expect(config.quality, equals(70));
        expect(config.generateThumbnail, isTrue);
        expect(config.keepOriginal, isFalse);
      });

      test('should create custom config with specified values', () {
        final config = MediaProcessingConfig(
          quality: 90,
          maxWidth: 1920,
          maxHeight: 1080,
          generateThumbnail: false,
          thumbnailQuality: 75,
        );

        expect(config.quality, equals(90));
        expect(config.maxWidth, equals(1920));
        expect(config.maxHeight, equals(1080));
        expect(config.generateThumbnail, isFalse);
        expect(config.thumbnailQuality, equals(75));
      });
    });

    group('MediaProcessingResult', () {
      test('should create result with correct properties', () {
        final result = MediaProcessingResult(
          originalUrl: 'https://example.com/image.jpg',
          thumbnailUrl: 'https://example.com/thumb.jpg',
          fileName: 'test_image.jpg',
          fileSize: 1024000,
          mediaType: MediaType.image,
          width: 1920,
          height: 1080,
          processingTime: Duration(seconds: 5),
        );

        expect(result.originalUrl, equals('https://example.com/image.jpg'));
        expect(result.thumbnailUrl, equals('https://example.com/thumb.jpg'));
        expect(result.fileName, equals('test_image.jpg'));
        expect(result.fileSize, equals(1024000));
        expect(result.mediaType, equals(MediaType.image));
        expect(result.width, equals(1920));
        expect(result.height, equals(1080));
        expect(result.isImage, isTrue);
        expect(result.isVideo, isFalse);
      });

      test('should format file size correctly', () {
        final smallResult = MediaProcessingResult(
          originalUrl: 'test.jpg',
          fileName: 'test.jpg',
          fileSize: 512,
          mediaType: MediaType.image,
          processingTime: Duration(seconds: 1),
        );
        expect(smallResult.formattedFileSize, equals('512B'));

        final mediumResult = MediaProcessingResult(
          originalUrl: 'test.jpg',
          fileName: 'test.jpg',
          fileSize: 1536,
          mediaType: MediaType.image,
          processingTime: Duration(seconds: 1),
        );
        expect(mediumResult.formattedFileSize, equals('1.5KB'));

        final largeResult = MediaProcessingResult(
          originalUrl: 'test.jpg',
          fileName: 'test.jpg',
          fileSize: 2097152,
          mediaType: MediaType.image,
          processingTime: Duration(seconds: 1),
        );
        expect(largeResult.formattedFileSize, equals('2.0MB'));
      });

      test('should format duration correctly for video', () {
        final result = MediaProcessingResult(
          originalUrl: 'test.mp4',
          fileName: 'test.mp4',
          fileSize: 1024000,
          mediaType: MediaType.video,
          duration: Duration(minutes: 2, seconds: 30),
          processingTime: Duration(seconds: 10),
        );

        expect(result.formattedDuration, equals('02:30'));
      });

      test('should convert to and from JSON correctly', () {
        final originalResult = MediaProcessingResult(
          originalUrl: 'https://example.com/image.jpg',
          thumbnailUrl: 'https://example.com/thumb.jpg',
          fileName: 'test_image.jpg',
          fileSize: 1024000,
          mediaType: MediaType.image,
          width: 1920,
          height: 1080,
          duration: Duration(minutes: 1, seconds: 30),
          compressionRatio: 0.75,
          processingTime: Duration(seconds: 5),
          metadata: {'test': 'value'},
        );

        final json = originalResult.toJson();
        final restoredResult = MediaProcessingResult.fromJson(json);

        expect(restoredResult.originalUrl, equals(originalResult.originalUrl));
        expect(
          restoredResult.thumbnailUrl,
          equals(originalResult.thumbnailUrl),
        );
        expect(restoredResult.fileName, equals(originalResult.fileName));
        expect(restoredResult.fileSize, equals(originalResult.fileSize));
        expect(restoredResult.mediaType, equals(originalResult.mediaType));
        expect(restoredResult.width, equals(originalResult.width));
        expect(restoredResult.height, equals(originalResult.height));
        expect(restoredResult.duration, equals(originalResult.duration));
        expect(
          restoredResult.compressionRatio,
          equals(originalResult.compressionRatio),
        );
        expect(restoredResult.metadata, equals(originalResult.metadata));
      });
    });

    group('SmartCompressionStrategy', () {
      test('should create high quality config', () {
        final config = SmartCompressionConfig.highQuality();

        expect(config.strategy, equals(CompressionStrategy.highQuality));
        expect(config.maxQuality, equals(95));
        expect(config.minQuality, equals(80));
      });

      test('should create balanced config', () {
        final config = SmartCompressionConfig.balanced();

        expect(config.strategy, equals(CompressionStrategy.balanced));
        expect(config.maxQuality, equals(85));
        expect(config.minQuality, equals(65));
      });

      test('should create high compression config', () {
        final config = SmartCompressionConfig.highCompression();

        expect(config.strategy, equals(CompressionStrategy.highCompression));
        expect(config.maxQuality, equals(75));
        expect(config.minQuality, equals(50));
      });
    });

    group('MemoryManager', () {
      test('should create and track temp files', () async {
        final manager = MemoryManager.instance;
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);

        final tempFile = await manager.createTempFile(
          data: testData,
          extension: 'test',
          purpose: 'unit test',
        );

        expect(await tempFile.exists(), isTrue);
        expect(await tempFile.readAsBytes(), equals(testData));

        final stats = manager.getMemoryStats();
        expect(stats.tempFilesCount, greaterThan(0));

        // 清理
        await manager.deleteTempFile(tempFile);
        expect(await tempFile.exists(), isFalse);
      });

      test('should get memory statistics', () {
        final manager = MemoryManager.instance;
        final stats = manager.getMemoryStats();

        expect(stats.totalAllocated, greaterThanOrEqualTo(0));
        expect(stats.currentUsage, greaterThanOrEqualTo(0));
        expect(stats.peakUsage, greaterThanOrEqualTo(0));
        expect(stats.tempFilesCount, greaterThanOrEqualTo(0));
        expect(stats.tempFilesSize, greaterThanOrEqualTo(0));
      });
    });

    group('UnifiedMediaService', () {
      test('should get supported extensions', () {
        final extensions = mediaService.getSupportedExtensions();

        expect(extensions, isNotEmpty);
        expect(extensions, contains('jpg'));
        expect(extensions, contains('png'));
        expect(extensions, contains('mp4'));
      });

      test('should check file support correctly', () {
        final imageFile = File('test.jpg');
        final videoFile = File('test.mp4');
        final unsupportedFile = File('test.txt');

        expect(mediaService.isFileSupported(imageFile), isTrue);
        expect(mediaService.isFileSupported(videoFile), isTrue);
        expect(mediaService.isFileSupported(unsupportedFile), isFalse);
      });

      test('should get service statistics', () {
        final stats = mediaService.getStatistics();

        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('factory_stats'), isTrue);
        expect(stats.containsKey('thumbnail_stats'), isTrue);
      });
    });
  });
}
